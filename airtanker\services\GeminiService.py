from main import airtanker_app
import os
import base64
from dotenv import load_dotenv
from google import genai
from google.genai import types


class GeminiService:
    """
    Service for interacting with Google's Gemini AI API
    """
    
    def __init__(self):
        load_dotenv()
        try:
            self.api_key = os.getenv("GOOGLE_GEMINI_API_KEY")
            if not self.api_key:
                raise ValueError("GOOGLE_GEMINI_API_KEY not found in environment variables")
            
            self.client = genai.Client(api_key=self.api_key)
            self.model = "gemini-2.5-pro"
            
        except Exception as e:
            airtanker_app.logger.exception("Error initializing GeminiService: %s", str(e))
            raise
    
    def generate_content_stream(self, prompt_text="Hello", response_schema=None):
        """
        Generate content using Gemini AI with streaming response

        Args:
            prompt_text (str): The text prompt to send to Gemini

        Returns:
            Generator: Streaming response chunks
        """
        try:
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_bytes(
                            mime_type="image/png",
                            data=base64.b64decode(
                                """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"""
                            ),
                        ),
                        types.Part.from_text(text=prompt_text),
                        types.Part.from_text(text="""INSERT_INPUT_HERE"""),
                    ],
                ),
            ]
            
            generate_content_config = {
                'thinkingConfig': {
                    'thinking_budget': -1
                },
                'response_mime_type': "application/json",
                'response_schema': response_schema
            }

            return self.client.models.generate_content_stream(
                model=self.model,
                contents=contents,
                config=generate_content_config,
            )
            
        except Exception as e:
            airtanker_app.logger.exception("Error generating content with Gemini: %s", str(e))
            raise
    
    def generate_content(self, prompt_text="Hello", response_schema=None):
        """
        Generate content using Gemini AI with single response

        Args:
            prompt_text (str): The text prompt to send to Gemini

        Returns:
            str: Complete response text
        """
        try:
            response_text = ""
            for chunk in self.generate_content_stream(prompt_text=prompt_text, response_schema=response_schema):
                if chunk.text:
                    response_text += chunk.text
            return response_text
            
        except Exception as e:
            airtanker_app.logger.exception("Error generating content with Gemini: %s", str(e))
            raise
    
    def print_stream_response(self, prompt_text="Hello"):
        """
        Generate content and print it to terminal in real-time

        Args:
            prompt_text (str): The text prompt to send to Gemini
        """
        try:
            print(f"\n=== Gemini AI Response ===")
            print(f"Prompt: {prompt_text}")
            print(f"Response:")

            for chunk in self.generate_content_stream(prompt_text):
                if chunk.text:
                    print(chunk.text, end="", flush=True)
            
            print(f"\n=== End Response ===\n")
            
        except Exception as e:
            airtanker_app.logger.exception("Error printing stream response: %s", str(e))
            print(f"Error: {str(e)}")

    def print_test(self):
        """
        Use this function to test the Gemini AI integration and new functionalitites
        """
        try:
            print("Starting test")
            test_prompt = "Hello! This is a test of the Gemini AI integration for AirTanker timesheet processing."

            client = genai.Client(
                api_key=self.api_key,
            )

            model = "gemini-2.5-pro"
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=test_prompt),
                    ],
                ),
            ]
            generate_content_config = types.GenerateContentConfig(
                thinking_config = types.ThinkingConfig(
                    thinking_budget=-1,
                ),
                response_mime_type="application/json",
            )

            for chunk in client.models.generate_content_stream(
                model=model,
                contents=contents,
                config=generate_content_config,
            ):
                print(chunk.text, end="")

            print("Test complete")

        except Exception as e:
            print(f"Error in test with Gemini AI: {e}")
